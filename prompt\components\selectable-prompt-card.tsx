'use client'

import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { 
  Copy, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar, 
  BarChart3,
  Tag
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import * as api from '@/lib/api/client'
import type { PromptWithDetails } from '@/lib/types'
import type { BatchSelectionManager } from '@/lib/batch-operations'

interface SelectablePromptCardProps {
  prompt: PromptWithDetails
  categoryName?: string
  categoryColor?: string
  tags?: Array<{ id: string; name: string; color: string }>
  selectionManager: BatchSelectionManager
  isSelected: boolean
  isSelectionMode: boolean
  onEdit?: (prompt: PromptWithDetails) => void
  onDelete?: (prompt: PromptWithDetails) => void
  onView?: (prompt: PromptWithDetails) => void
  onCopySuccess?: () => void
}

export function SelectablePromptCard({
  prompt,
  categoryName,
  categoryColor,
  tags = [],
  selectionManager,
  isSelected,
  isSelectionMode,
  onEdit,
  onDelete,
  onView,
  onCopySuccess
}: SelectablePromptCardProps) {
  const { toast } = useToast()
  const [isCopying, setIsCopying] = useState(false)

  // 处理复制
  const handleCopy = async (e: React.MouseEvent) => {
    e.stopPropagation()
    
    if (isCopying) return

    setIsCopying(true)
    try {
      await navigator.clipboard.writeText(prompt.content)
      
      // 增加使用次数
      await api.incrementUsageCount(prompt.id)
      
      toast({
        title: "复制成功",
        description: "提示词已复制到剪贴板",
      })
      
      if (onCopySuccess) {
        onCopySuccess()
      }
    } catch (error) {
      toast({
        title: "复制失败",
        description: "无法复制到剪贴板",
        variant: "destructive",
      })
    } finally {
      setIsCopying(false)
    }
  }

  // 处理选择
  const handleSelect = (e: React.MouseEvent) => {
    e.stopPropagation()
    selectionManager.toggle(prompt.id)
  }

  // 处理卡片点击
  const handleCardClick = () => {
    if (isSelectionMode) {
      selectionManager.toggle(prompt.id)
    } else if (onView) {
      onView(prompt)
    }
  }

  // 处理编辑
  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onEdit) {
      onEdit(prompt)
    }
  }

  // 处理删除
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onDelete) {
      onDelete(prompt)
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { 
        addSuffix: true, 
        locale: zhCN 
      })
    } catch {
      return '未知时间'
    }
  }

  return (
    <Card 
      className={`group cursor-pointer transition-all duration-200 hover:shadow-md ${
        isSelected ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950' : ''
      } ${isSelectionMode ? 'hover:bg-gray-50 dark:hover:bg-gray-800' : ''}`}
      onClick={handleCardClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            {/* 选择复选框 */}
            {isSelectionMode && (
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => selectionManager.toggle(prompt.id)}
                onClick={handleSelect}
                className="mt-1"
              />
            )}
            
            {/* 标题和分类 */}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                {prompt.title}
              </h3>
              
              {/* 分类标签 */}
              {categoryName && (
                <div className="flex items-center gap-1 mb-2">
                  <Tag className="h-3 w-3 text-gray-500" />
                  <Badge
                    variant="secondary"
                    className="text-xs"
                    style={categoryColor ? {
                      backgroundColor: `${categoryColor}20`,
                      color: categoryColor,
                      borderColor: `${categoryColor}40`
                    } : undefined}
                  >
                    {categoryName}
                  </Badge>
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          {!isSelectionMode && (
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {onView && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onView(prompt)
                  }}
                  className="h-8 w-8 p-0"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              )}
              
              {onEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleEdit}
                  className="h-8 w-8 p-0"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              
              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDelete}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* 内容预览 */}
        <div className="mb-4">
          <div className="flex items-start gap-3">
            <Copy className="h-4 w-4 text-gray-400 mt-1 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3 leading-relaxed">
                {prompt.content}
              </p>
            </div>
          </div>
        </div>

        {/* 标签 */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {tags.slice(0, 4).map(tag => (
              <Badge 
                key={tag.id} 
                variant="outline" 
                className="text-xs"
                style={{ borderColor: tag.color, color: tag.color }}
              >
                {tag.name}
              </Badge>
            ))}
            {tags.length > 4 && (
              <Badge variant="outline" className="text-xs text-gray-500">
                +{tags.length - 4}
              </Badge>
            )}
          </div>
        )}

        {/* 底部信息和操作 */}
        <div className="flex items-center justify-between">
          {/* 统计信息 */}
          <div className="flex items-center gap-4 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <BarChart3 className="h-3 w-3" />
              <span>{prompt.usageCount || 0} 次使用</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(prompt.updatedAt)}</span>
            </div>
          </div>

          {/* 复制按钮 */}
          {!isSelectionMode && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopy}
              disabled={isCopying}
              className="h-8"
            >
              <Copy className="h-3 w-3 mr-1" />
              {isCopying ? '复制中...' : '复制'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
