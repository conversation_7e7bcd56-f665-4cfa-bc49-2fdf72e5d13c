/**
 * 数据导入/导出工具
 * 支持 JSON 和 CSV 格式的数据导入导出
 */

import * as api from '@/lib/api/client'

// 导出数据类型定义
export interface ExportData {
  prompts: any[]
  categories: any[]
  tags: any[]
  exportTime: string
  version: string
}

// 导入结果类型
export interface ImportResult {
  success: boolean
  imported: {
    prompts: number
    categories: number
    tags: number
  }
  errors: string[]
  warnings: string[]
}

/**
 * 导出所有数据为 JSON 格式
 */
export async function exportDataAsJSON(): Promise<string> {
  try {
    console.log('🚀 开始导出数据...')
    
    // 并行获取所有数据
    const [prompts, categories, tags] = await Promise.all([
      api.getPrompts({ limit: 10000 }), // 获取所有提示词
      api.getCategories(),
      api.getTags()
    ])

    const exportData: ExportData = {
      prompts: prompts.data,
      categories,
      tags,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }

    console.log(`✅ 导出完成: ${prompts.data.length} 个提示词, ${categories.length} 个分类, ${tags.length} 个标签`)
    
    return JSON.stringify(exportData, null, 2)
  } catch (error) {
    console.error('导出数据失败:', error)
    throw new Error('导出数据失败: ' + (error as Error).message)
  }
}

/**
 * 导出提示词为 CSV 格式
 */
export async function exportPromptsAsCSV(): Promise<string> {
  try {
    console.log('🚀 开始导出提示词为 CSV...')
    
    const prompts = await api.getPrompts({ limit: 10000 })
    const categories = await api.getCategories()
    
    // 创建分类映射
    const categoryMap = new Map(categories.map(cat => [cat.id, cat.name]))
    
    // CSV 头部
    const headers = [
      'ID',
      '标题',
      '内容',
      '分类',
      '使用次数',
      '创建时间',
      '更新时间'
    ]
    
    // CSV 数据行
    const rows = prompts.data.map(prompt => [
      prompt.id,
      `"${prompt.title.replace(/"/g, '""')}"`, // 转义双引号
      `"${prompt.content.replace(/"/g, '""')}"`,
      categoryMap.get(prompt.categoryId) || '未分类',
      prompt.usageCount || 0,
      prompt.createdAt,
      prompt.updatedAt
    ])
    
    // 组合 CSV 内容
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n')
    
    console.log(`✅ CSV 导出完成: ${prompts.data.length} 个提示词`)
    
    return csvContent
  } catch (error) {
    console.error('导出 CSV 失败:', error)
    throw new Error('导出 CSV 失败: ' + (error as Error).message)
  }
}

/**
 * 下载文件到本地
 */
export function downloadFile(content: string, filename: string, mimeType: string = 'text/plain') {
  const blob = new Blob([content], { type: mimeType })
  const url = URL.createObjectURL(blob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  // 清理 URL 对象
  setTimeout(() => URL.revokeObjectURL(url), 100)
}

/**
 * 导出并下载 JSON 文件
 */
export async function exportAndDownloadJSON() {
  try {
    const jsonData = await exportDataAsJSON()
    const filename = `prompts-export-${new Date().toISOString().split('T')[0]}.json`
    downloadFile(jsonData, filename, 'application/json')
    return { success: true, filename }
  } catch (error) {
    console.error('导出 JSON 文件失败:', error)
    return { success: false, error: (error as Error).message }
  }
}

/**
 * 导出并下载 CSV 文件
 */
export async function exportAndDownloadCSV() {
  try {
    const csvData = await exportPromptsAsCSV()
    const filename = `prompts-export-${new Date().toISOString().split('T')[0]}.csv`
    downloadFile(csvData, filename, 'text/csv')
    return { success: true, filename }
  } catch (error) {
    console.error('导出 CSV 文件失败:', error)
    return { success: false, error: (error as Error).message }
  }
}

/**
 * 验证导入数据格式
 */
export function validateImportData(data: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  
  if (!data || typeof data !== 'object') {
    errors.push('数据格式无效')
    return { valid: false, errors }
  }
  
  // 检查必需字段
  if (!Array.isArray(data.prompts)) {
    errors.push('缺少提示词数据或格式错误')
  }
  
  if (!Array.isArray(data.categories)) {
    errors.push('缺少分类数据或格式错误')
  }
  
  // 检查提示词数据结构
  if (data.prompts && data.prompts.length > 0) {
    const firstPrompt = data.prompts[0]
    if (!firstPrompt.title || !firstPrompt.content) {
      errors.push('提示词数据缺少必需字段 (title, content)')
    }
  }
  
  return { valid: errors.length === 0, errors }
}

/**
 * 从 JSON 数据导入
 */
export async function importFromJSON(jsonData: string): Promise<ImportResult> {
  const result: ImportResult = {
    success: false,
    imported: { prompts: 0, categories: 0, tags: 0 },
    errors: [],
    warnings: []
  }
  
  try {
    // 解析 JSON
    let data: any
    try {
      data = JSON.parse(jsonData)
    } catch (error) {
      result.errors.push('JSON 格式错误')
      return result
    }
    
    // 验证数据格式
    const validation = validateImportData(data)
    if (!validation.valid) {
      result.errors.push(...validation.errors)
      return result
    }
    
    console.log('🚀 开始导入数据...')
    
    // 导入分类
    if (data.categories && data.categories.length > 0) {
      for (const category of data.categories) {
        try {
          // 检查分类是否已存在
          const exists = await api.checkCategoryNameExists(category.name)
          if (!exists) {
            await api.createCategory({
              name: category.name,
              description: category.description || '',
              color: category.color || '#6366f1',
              icon: category.icon || 'folder',
              sortOrder: category.sortOrder || 0
            })
            result.imported.categories++
          } else {
            result.warnings.push(`分类 "${category.name}" 已存在，跳过`)
          }
        } catch (error) {
          result.errors.push(`导入分类 "${category.name}" 失败: ${(error as Error).message}`)
        }
      }
    }
    
    // 重新获取分类以建立映射
    const categories = await api.getCategories()
    const categoryMap = new Map(categories.map(cat => [cat.name, cat.id]))
    
    // 导入提示词
    if (data.prompts && data.prompts.length > 0) {
      for (const prompt of data.prompts) {
        try {
          // 查找对应的分类ID
          let categoryId = null
          if (prompt.categoryId) {
            // 如果有分类信息，尝试匹配
            const category = data.categories?.find((cat: any) => cat.id === prompt.categoryId)
            if (category) {
              categoryId = categoryMap.get(category.name) || null
            }
          }
          
          await api.createPrompt({
            title: prompt.title,
            content: prompt.content,
            categoryId,
            description: prompt.description || ''
          })
          result.imported.prompts++
        } catch (error) {
          result.errors.push(`导入提示词 "${prompt.title}" 失败: ${(error as Error).message}`)
        }
      }
    }
    
    result.success = result.imported.prompts > 0 || result.imported.categories > 0
    console.log(`✅ 导入完成: ${result.imported.prompts} 个提示词, ${result.imported.categories} 个分类`)
    
  } catch (error) {
    result.errors.push('导入过程中发生错误: ' + (error as Error).message)
  }
  
  return result
}
